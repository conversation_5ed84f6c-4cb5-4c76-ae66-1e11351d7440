#!/bin/bash
# TensorFlow 1.x Environment Setup Script for Linux/Mac
# This script sets up a TensorFlow 1.x environment to resolve compatibility issues

set -e  # Exit on any error

echo "========================================"
echo "TensorFlow 1.x Environment Setup"
echo "========================================"

ENV_NAME="tft_tf1"
PYTHON_VERSION="3.7"

echo "Environment name: $ENV_NAME"
echo "Python version: $PYTHON_VERSION"
echo "========================================"

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ ERROR: Conda not found. Please install Miniconda or Anaconda first."
    echo "Download from: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "✅ Conda found: $(conda --version)"

# Remove existing environment if it exists
echo "Removing existing environment $ENV_NAME (if exists)..."
conda env remove -n "$ENV_NAME" -y 2>/dev/null || true

# Create new environment
echo "Creating new environment $ENV_NAME with Python $PYTHON_VERSION..."
conda create -n "$ENV_NAME" python="$PYTHON_VERSION" -y

echo "✅ Environment created successfully"

# Activate environment and install packages
echo "Installing TensorFlow 1.x packages..."

# Install TensorFlow 1.x
echo "Installing tensorflow==1.15.0..."
conda run -n "$ENV_NAME" pip install tensorflow==1.15.0

echo "Installing keras==2.3.1..."
conda run -n "$ENV_NAME" pip install keras==2.3.1

echo "Installing additional dependencies..."
conda run -n "$ENV_NAME" pip install numpy==1.19.5 pandas scikit-learn matplotlib seaborn

# Install project requirements if available
if [ -f "requirements.txt" ]; then
    echo "Installing from requirements.txt..."
    conda run -n "$ENV_NAME" pip install -r requirements.txt || echo "Warning: Some packages from requirements.txt may have failed to install"
fi

# Verify installation
echo "========================================"
echo "Verifying installation..."
echo "========================================"

conda run -n "$ENV_NAME" python -c "
import tensorflow as tf
print(f'TensorFlow version: {tf.__version__}')
print(f'Keras version: {tf.keras.__version__}')

# Test basic operation
hello = tf.constant('Hello, TensorFlow!')
sess = tf.Session()
result = sess.run(hello)
print(f'Test result: {result}')
sess.close()
print('✅ TensorFlow 1.x verification successful')
"

# Create activation script
cat > activate_tf1_env.sh << 'EOF'
#!/bin/bash
# TensorFlow 1.x Environment Activation Script
# Generated by setup_tf1_environment.sh

echo "🚀 Activating TensorFlow 1.x environment: tft_tf1"
echo "📍 Current directory: $(pwd)"

# Activate conda environment
conda activate tft_tf1

# Verify TensorFlow installation
echo "🔍 Verifying TensorFlow installation..."
python -c "import tensorflow as tf; print(f'TensorFlow version: {tf.__version__}'); print(f'Keras version: {tf.keras.__version__}')"

echo "✅ Environment ready! You can now run:"
echo "   python -m script_train_fixed_params volatility . no"
echo "   python -m script_hyperparam_opt volatility . no"
EOF

chmod +x activate_tf1_env.sh

echo "========================================"
echo "🎉 SETUP COMPLETE!"
echo "========================================"
echo "✅ Environment '$ENV_NAME' created successfully"
echo "✅ TensorFlow 1.x installed and verified"
echo "✅ Activation script created: activate_tf1_env.sh"
echo ""
echo "📋 Next steps:"
echo "1. Activate environment: conda activate $ENV_NAME"
echo "2. Or use the script: ./activate_tf1_env.sh"
echo "3. Run training: python -m script_train_fixed_params volatility . no"
echo "4. Run hyperparameter optimization: python -m script_hyperparam_opt volatility . no"
