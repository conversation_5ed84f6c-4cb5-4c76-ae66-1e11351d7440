#!/usr/bin/env python3
"""
Test script to verify KerasTensor fixes in TF 2.x compatibility mode.

This script tests the specific fixes applied to resolve KerasTensor errors
while maintaining TensorFlow 2.x compatibility.
"""

import sys
import traceback
import numpy as np


def test_tensor_operations():
    """Test the fixed tensor operations."""
    print("🔍 Testing fixed tensor operations...")
    
    try:
        import tensorflow.compat.v1 as tf
        tf.compat.v1.disable_v2_behavior()
        
        # Test the operations that were causing issues
        print("Testing tf.concat (was tf.keras.backend.concatenate)...")
        tensor1 = tf.constant([[1, 2], [3, 4]])
        tensor2 = tf.constant([[5, 6], [7, 8]])
        result = tf.concat([tensor1, tensor2], axis=0)
        print("✅ tf.concat works")
        
        print("Testing tf.stack (was tf.keras.backend.stack)...")
        result = tf.stack([tensor1, tensor2], axis=0)
        print("✅ tf.stack works")
        
        print("Testing tf.expand_dims (was K.expand_dims)...")
        result = tf.expand_dims(tensor1, axis=-1)
        print("✅ tf.expand_dims works")
        
        print("Testing tf.reduce_sum (was K.sum)...")
        result = tf.reduce_sum(tensor1, axis=1)
        print("✅ tf.reduce_sum works")
        
        print("Testing tf.reshape (was K.reshape)...")
        result = tf.reshape(tensor1, [-1])
        print("✅ tf.reshape works")
        
        return True
        
    except Exception as e:
        print(f"❌ Tensor operations test failed: {e}")
        traceback.print_exc()
        return False


def test_model_import():
    """Test that the TFT model can be imported without KerasTensor errors."""
    print("\n🔍 Testing TFT model import...")
    
    try:
        from libs.tft_model import TemporalFusionTransformer
        print("✅ TFT model imported successfully")
        
        # Test that the fixed aliases work
        from libs import tft_model
        
        # Test concat function
        test_tensors = [tf.constant([1, 2]), tf.constant([3, 4])]
        result = tft_model.concat(test_tensors, axis=0)
        print("✅ Fixed concat function works")
        
        # Test stack function  
        result = tft_model.stack(test_tensors, axis=0)
        print("✅ Fixed stack function works")
        
        return True
        
    except Exception as e:
        print(f"❌ Model import test failed: {e}")
        traceback.print_exc()
        return False


def test_basic_model_creation():
    """Test basic model creation without full training."""
    print("\n🔍 Testing basic model creation...")
    
    try:
        import tensorflow.compat.v1 as tf
        tf.compat.v1.disable_v2_behavior()
        
        from libs.tft_model import TemporalFusionTransformer
        import expt_settings.configs
        
        # Get basic config
        config = expt_settings.configs.ExperimentConfig('volatility', '.')
        
        # Create minimal parameters
        params = {
            'total_time_steps': 252,
            'num_encoder_steps': 168,
            'num_epochs': 1,
            'early_stopping_patience': 5,
            'multiprocessing_workers': 1,
            'hidden_layer_size': 160,
            'dropout_rate': 0.1,
            'max_gradient_norm': 1.0,
            'learning_rate': 0.001,
            'minibatch_size': 64,
            'num_heads': 4,
            'stack_size': 1,
            'input_size': config.data_formatter.get_num_cols(),
            'output_size': config.data_formatter.num_classes_per_cat_input[0] if config.data_formatter.num_classes_per_cat_input else 1,
            'category_counts': config.data_formatter.num_classes_per_cat_input,
            'num_categorical_variables': len(config.data_formatter.num_classes_per_cat_input),
            'num_regular_variables': config.data_formatter.get_num_cols() - len(config.data_formatter.num_classes_per_cat_input),
            'quantiles': [0.1, 0.5, 0.9]
        }
        
        print("Creating TFT model instance...")
        model = TemporalFusionTransformer(params, use_cudnn=False)
        print("✅ TFT model created successfully")
        
        print(f"✅ Model name: {model.name}")
        print(f"✅ Model time steps: {model.time_steps}")
        print(f"✅ Model input size: {model.input_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation test failed: {e}")
        traceback.print_exc()
        return False


def test_model_building():
    """Test model building process that was causing KerasTensor errors."""
    print("\n🔍 Testing model building process...")
    
    try:
        import tensorflow.compat.v1 as tf
        tf.compat.v1.disable_v2_behavior()
        
        from libs.tft_model import TemporalFusionTransformer
        import expt_settings.configs
        
        # Get basic config
        config = expt_settings.configs.ExperimentConfig('volatility', '.')
        
        # Create minimal parameters
        params = {
            'total_time_steps': 252,
            'num_encoder_steps': 168,
            'num_epochs': 1,
            'early_stopping_patience': 5,
            'multiprocessing_workers': 1,
            'hidden_layer_size': 160,
            'dropout_rate': 0.1,
            'max_gradient_norm': 1.0,
            'learning_rate': 0.001,
            'minibatch_size': 64,
            'num_heads': 4,
            'stack_size': 1,
            'input_size': config.data_formatter.get_num_cols(),
            'output_size': config.data_formatter.num_classes_per_cat_input[0] if config.data_formatter.num_classes_per_cat_input else 1,
            'category_counts': config.data_formatter.num_classes_per_cat_input,
            'num_categorical_variables': len(config.data_formatter.num_classes_per_cat_input),
            'num_regular_variables': config.data_formatter.get_num_cols() - len(config.data_formatter.num_classes_per_cat_input),
            'quantiles': [0.1, 0.5, 0.9]
        }
        
        print("Creating TFT model and building graph...")
        model = TemporalFusionTransformer(params, use_cudnn=False)
        
        # This is where KerasTensor errors typically occurred
        print("Building model (this was causing KerasTensor errors)...")
        built_model = model.build_model()
        print("✅ Model built successfully without KerasTensor errors!")
        
        return True
        
    except Exception as e:
        print(f"❌ Model building test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all KerasTensor fix tests."""
    print("=" * 60)
    print("KerasTensor Fixes Test Suite")
    print("=" * 60)
    
    tests = [
        ("Tensor Operations", test_tensor_operations),
        ("Model Import", test_model_import),
        ("Basic Model Creation", test_basic_model_creation),
        ("Model Building", test_model_building),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! KerasTensor fixes are working correctly.")
        print("\n📋 Next steps:")
        print("1. Run: python -m script_train_fixed_params volatility . no")
        print("2. Verify full training pipeline works")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. KerasTensor issues may persist.")
        print("\n🔧 Troubleshooting:")
        print("1. Check that all K.* functions were replaced")
        print("2. Verify concat/stack aliases are using tf.* functions")
        print("3. Ensure TF compat.v1 mode is enabled")
        return 1


if __name__ == "__main__":
    sys.exit(main())
