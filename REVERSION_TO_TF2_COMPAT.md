# Reversion to TensorFlow 2.x Compatibility Mode

## 🔄 Changes Reverted

Based on your concern about potential instability with other scripts, I have reverted the codebase back to TensorFlow 2.x compatibility mode while keeping the improved session handling.

## ✅ Files Restored

### 1. `libs/utils.py`
**Reverted to:**
```python
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()
```

### 2. `libs/tft_model.py`
**Reverted to:**
```python
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()
```

### 3. `script_train_fixed_params.py`
**Reverted to:**
```python
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()
```

**With improved session handling:**
```python
# Tensorflow setup
try:
  default_keras_session = tf.keras.backend.get_session()
except AttributeError:
  # For newer TensorFlow versions, create a default session
  default_keras_session = tf.Session()

# Set session for TensorFlow 1.x compatibility
try:
  tf.keras.backend.set_session(sess)
except AttributeError:
  # For newer TensorFlow versions, session management is automatic
  pass
```

### 4. `script_hyperparam_opt.py`
**Reverted to:**
```python
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()
```

**With improved session handling (same pattern as above)**

## 🎯 Current State

### What's Maintained
- ✅ **TensorFlow 2.x compatibility** - Uses `tensorflow.compat.v1`
- ✅ **Improved session handling** - Better error handling for session operations
- ✅ **Backward compatibility** - Works with existing TF 2.x environment
- ✅ **Stability** - No impact on other scripts using TF 2.x

### What's Improved from Original
- ✅ **Better error handling** - Try-catch blocks for session operations
- ✅ **Graceful degradation** - Falls back when TF 1.x APIs not available
- ✅ **Cleaner code** - More robust session management

## 🔍 Current Issue Status

### The Original Problem Remains
```
ValueError: A KerasTensor cannot be used as input to a TensorFlow function. 
A KerasTensor is a symbolic placeholder for a shape and dtype, used when 
constructing Keras Functional models or Keras Functions.
```

**This error will still occur** because the root cause (mixed TF 1.x/2.x API usage) hasn't been addressed.

## 🛠️ Alternative Solutions

Since you prefer to maintain TF 2.x compatibility, here are alternative approaches to resolve the KerasTensor issue:

### Option 1: Targeted Fixes (Recommended)
Instead of changing the entire environment, we can fix specific problematic operations:

```python
# Instead of mixing Keras tensors with TF operations, use:
@tf.function
def safe_tensor_operation(inputs):
    # Wrap problematic operations in tf.function
    return tf.some_operation(inputs)
```

### Option 2: Keras Layer Wrapping
Wrap problematic TensorFlow operations in Keras layers:

```python
class SafeTensorOperation(tf.keras.layers.Layer):
    def call(self, inputs):
        # Safe tensor operations here
        return processed_inputs
```

### Option 3: Conditional TF Version Handling
Add version-specific code paths:

```python
import tensorflow as tf

if tf.__version__.startswith('2.'):
    # TF 2.x specific handling
    def handle_tensor_operation(tensor):
        return tf.compat.v1.Session().run(tensor)
else:
    # TF 1.x handling
    def handle_tensor_operation(tensor):
        return tensor
```

## 📋 Next Steps Options

### Immediate Options:

1. **Keep Current State** - Use the improved session handling, accept the KerasTensor error for now
2. **Implement Targeted Fixes** - Fix specific problematic operations without changing the overall approach
3. **Create Isolated Environment** - Use TF 1.x only for TFT training, keep TF 2.x for other scripts

### Recommended Approach:
**Option 2: Implement Targeted Fixes**

This would involve:
- Identifying the specific lines causing KerasTensor errors
- Wrapping them in appropriate TF 2.x compatible operations
- Maintaining overall TF 2.x compatibility

## 🔧 Files Available for Reference

The TF 1.x setup files are still available if needed later:
- `setup_tf1_environment.py`
- `TF1_ENVIRONMENT_SETUP.md`
- `TF2_MIGRATION_STRATEGY.md`
- All setup scripts and documentation

## ⚠️ Current Limitations

1. **KerasTensor Error Persists** - The original issue is not resolved
2. **Mixed API Usage** - Still using TF 1.x APIs within TF 2.x environment
3. **Potential Instability** - The compatibility mode may have edge cases

## 🎯 Recommendation

Would you like me to:

1. **Implement targeted fixes** for the specific KerasTensor errors?
2. **Identify the exact problematic lines** and propose minimal changes?
3. **Create a hybrid approach** that fixes only the TFT model without affecting other scripts?

This would allow us to resolve the compatibility issue while maintaining your preference for TF 2.x compatibility.

---

**Status**: ✅ **REVERTED TO TF 2.x COMPATIBILITY**  
**Next Action**: Choose approach for resolving KerasTensor errors within TF 2.x framework
