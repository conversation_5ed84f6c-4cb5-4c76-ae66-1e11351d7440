#!/usr/bin/env python3
"""
Simple test to verify KerasTensor fixes work.
"""

import sys
import traceback


def test_core_fixes():
    """Test the core KerasTensor fixes."""
    print("🔍 Testing core KerasTensor fixes...")
    
    try:
        import tensorflow.compat.v1 as tf
        tf.compat.v1.disable_v2_behavior()
        
        # Test basic tensor operations
        print("Testing basic tensor operations...")
        tensor1 = tf.constant([[1, 2], [3, 4]])
        tensor2 = tf.constant([[5, 6], [7, 8]])
        
        # These operations were causing KerasTensor errors
        concat_result = tf.concat([tensor1, tensor2], axis=0)
        stack_result = tf.stack([tensor1, tensor2], axis=0)
        expand_result = tf.expand_dims(tensor1, axis=-1)
        sum_result = tf.reduce_sum(tensor1, axis=1)
        reshape_result = tf.reshape(tensor1, [-1])
        
        print("✅ All tensor operations work")
        return True
        
    except Exception as e:
        print(f"❌ Core fixes test failed: {e}")
        traceback.print_exc()
        return False


def test_model_aliases():
    """Test that the model aliases are fixed."""
    print("\n🔍 Testing model aliases...")
    
    try:
        import tensorflow.compat.v1 as tf
        tf.compat.v1.disable_v2_behavior()
        
        # Import the model to test aliases
        from libs import tft_model
        
        # Test that concat and stack are now tf functions, not keras backend
        print("Testing concat alias...")
        test_tensors = [tf.constant([1, 2]), tf.constant([3, 4])]
        result = tft_model.concat(test_tensors, axis=0)
        print("✅ concat alias works")
        
        print("Testing stack alias...")
        result = tft_model.stack(test_tensors, axis=0)
        print("✅ stack alias works")
        
        # Verify they're the right functions
        assert tft_model.concat == tf.concat, "concat should be tf.concat"
        assert tft_model.stack == tf.stack, "stack should be tf.stack"
        print("✅ Aliases point to correct TF functions")
        
        return True
        
    except Exception as e:
        print(f"❌ Model aliases test failed: {e}")
        traceback.print_exc()
        return False


def test_model_import():
    """Test that the model can be imported."""
    print("\n🔍 Testing model import...")
    
    try:
        from libs.tft_model import TemporalFusionTransformer
        print("✅ TFT model imported successfully")
        
        # Test some basic attributes
        print("✅ Model class accessible")
        return True
        
    except Exception as e:
        print(f"❌ Model import test failed: {e}")
        traceback.print_exc()
        return False


def test_no_kerastensor_in_operations():
    """Test that operations don't create KerasTensors."""
    print("\n🔍 Testing that operations don't create KerasTensors...")
    
    try:
        import tensorflow.compat.v1 as tf
        tf.compat.v1.disable_v2_behavior()
        
        # Create test tensors
        tensor1 = tf.constant([[1, 2], [3, 4]], dtype=tf.float32)
        tensor2 = tf.constant([[5, 6], [7, 8]], dtype=tf.float32)
        
        # Test operations that were problematic
        concat_result = tf.concat([tensor1, tensor2], axis=0)
        stack_result = tf.stack([tensor1, tensor2], axis=0)
        
        # Check tensor types - they should NOT be KerasTensors
        print(f"concat result type: {type(concat_result)}")
        print(f"stack result type: {type(stack_result)}")
        
        # KerasTensors have 'KerasTensor' in their type name
        concat_type_str = str(type(concat_result))
        stack_type_str = str(type(stack_result))
        
        if 'KerasTensor' in concat_type_str:
            print("❌ concat still creates KerasTensors")
            return False
        
        if 'KerasTensor' in stack_type_str:
            print("❌ stack still creates KerasTensors")
            return False
            
        print("✅ Operations create proper TF tensors, not KerasTensors")
        return True
        
    except Exception as e:
        print(f"❌ KerasTensor check failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run simple KerasTensor fix tests."""
    print("=" * 50)
    print("Simple KerasTensor Fixes Test")
    print("=" * 50)
    
    tests = [
        ("Core Fixes", test_core_fixes),
        ("Model Aliases", test_model_aliases),
        ("Model Import", test_model_import),
        ("No KerasTensor Creation", test_no_kerastensor_in_operations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ KerasTensor fixes are working correctly")
        print("✅ TF 2.x compatibility maintained")
        print("\n📋 The fixes should resolve the original error:")
        print("   'ValueError: A KerasTensor cannot be used as input to a TensorFlow function'")
        print("\n🚀 You can now try running the training script:")
        print("   python -m script_train_fixed_params volatility . no")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        print("Some KerasTensor issues may persist.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
