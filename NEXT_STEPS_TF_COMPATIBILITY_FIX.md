# Next Steps: TensorFlow Compatibility Fix

## Current Issue

The TFT model encounters a **TensorFlow 1.x/2.x API mixing issue** during model building:

```
ValueError: A KerasTensor cannot be used as input to a TensorFlow function. 
A KerasTensor is a symbolic placeholder for a shape and dtype, used when 
constructing Keras Functional models or Keras Functions.
```

**Root Cause**: The code mixes TensorFlow 1.x style operations with TensorFlow 2.x Keras tensors, which is incompatible even with `tf.compat.v1.disable_v2_behavior()`.

## Solution Options

### Option A: Quick Fix - TensorFlow 1.x Environment (Recommended for Immediate Use)

**Pros**: 
- Minimal code changes required
- Guaranteed compatibility
- Fast implementation

**Cons**: 
- Uses deprecated TensorFlow version
- Limited long-term viability

#### Implementation Steps:

1. **Create TensorFlow 1.x Environment**
```bash
# Create new conda environment
conda create -n tft_tf1 python=3.7
conda activate tft_tf1

# Install TensorFlow 1.x
pip install tensorflow==1.15.0
pip install keras==2.3.1

# Install other dependencies
pip install pandas numpy scikit-learn matplotlib seaborn
```

2. **Minimal Code Adjustments**
```python
# In all Python files, replace:
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()

# With:
import tensorflow as tf
```

3. **Test the Pipeline**
```bash
python -m script_train_fixed_params volatility . no
```

### Option B: TensorFlow 2.x Native Migration (Recommended for Production)

**Pros**: 
- Modern, supported TensorFlow version
- Better performance and features
- Long-term maintainability

**Cons**: 
- Requires significant code refactoring
- Time-intensive implementation

#### Implementation Steps:

1. **Model Architecture Refactoring**
   - Convert TF 1.x graph-based model to TF 2.x Keras model
   - Replace `tf.variable_scope` with Keras layers
   - Update session-based training to eager execution

2. **Key Areas to Refactor**:

##### A. Model Building (`libs/tft_model.py`)
```python
# Current (TF 1.x style):
with tf.variable_scope(self.name):
    all_inputs = tf.placeholder(...)
    
# Target (TF 2.x style):
class TFTModel(tf.keras.Model):
    def __init__(self, ...):
        super().__init__()
        # Initialize layers
        
    def call(self, inputs):
        # Forward pass logic
```

##### B. Training Loop
```python
# Current (Session-based):
with tf.Session() as sess:
    sess.run(...)
    
# Target (Eager execution):
@tf.function
def train_step(inputs, targets):
    with tf.GradientTape() as tape:
        predictions = model(inputs)
        loss = loss_fn(targets, predictions)
    gradients = tape.gradient(loss, model.trainable_variables)
    optimizer.apply_gradients(zip(gradients, model.trainable_variables))
```

##### C. Tensor Operations
```python
# Current (Mixed TF 1.x/2.x):
static_inputs = tf.keras.backend.stack(static_inputs, axis=1)

# Target (Pure TF 2.x):
static_inputs = tf.stack(static_inputs, axis=1)
```

### Option C: Alternative Libraries (Recommended for New Projects)

**Consider modern alternatives**:
- **PyTorch Forecasting**: Modern, well-maintained TFT implementation
- **Darts**: Comprehensive time series forecasting library
- **TensorFlow Probability**: Native TF 2.x time series models

## Detailed Implementation Plan for Option B

### Phase 1: Core Model Conversion (Estimated: 2-3 days)

1. **Create TF 2.x Model Class**
```python
# File: libs/tft_model_v2.py
class TemporalFusionTransformerV2(tf.keras.Model):
    def __init__(self, config):
        super().__init__()
        self.config = config
        self._build_layers()
    
    def _build_layers(self):
        # Initialize all layers as class attributes
        self.embedding_layers = {}
        self.attention_layers = {}
        # ... etc
    
    def call(self, inputs, training=None):
        # Forward pass implementation
        pass
```

2. **Convert Variable Scopes to Keras Layers**
```python
# Replace tf.variable_scope with custom Keras layers
class VariableSelectionNetwork(tf.keras.layers.Layer):
    def __init__(self, hidden_size, **kwargs):
        super().__init__(**kwargs)
        self.hidden_size = hidden_size
        
    def build(self, input_shape):
        # Create layer weights
        super().build(input_shape)
        
    def call(self, inputs):
        # Layer logic
        return outputs
```

3. **Update Tensor Shape Handling**
```python
# Replace get_shape() calls with tf.shape() or .shape
# Current:
shape = tensor.get_shape().as_list()

# Fixed:
shape = tf.shape(tensor)  # For dynamic shapes
# or
shape = tensor.shape  # For static shapes
```

### Phase 2: Training Pipeline Update (Estimated: 1-2 days)

1. **Convert to Eager Execution**
```python
# File: libs/training_v2.py
class TFTTrainer:
    def __init__(self, model, optimizer, loss_fn):
        self.model = model
        self.optimizer = optimizer
        self.loss_fn = loss_fn
    
    @tf.function
    def train_step(self, inputs, targets):
        with tf.GradientTape() as tape:
            predictions = self.model(inputs, training=True)
            loss = self.loss_fn(targets, predictions)
        
        gradients = tape.gradient(loss, self.model.trainable_variables)
        self.optimizer.apply_gradients(zip(gradients, self.model.trainable_variables))
        return loss
```

2. **Update Data Pipeline**
```python
# Use tf.data for efficient data loading
def create_dataset(data, batch_size):
    dataset = tf.data.Dataset.from_tensor_slices(data)
    dataset = dataset.batch(batch_size)
    dataset = dataset.prefetch(tf.data.AUTOTUNE)
    return dataset
```

### Phase 3: Integration and Testing (Estimated: 1 day)

1. **Update Main Scripts**
   - Modify `script_train_fixed_params.py` to use new model
   - Update `script_hyperparam_opt.py` for TF 2.x compatibility

2. **Comprehensive Testing**
   - Unit tests for model components
   - Integration tests for full pipeline
   - Performance benchmarking

## Immediate Action Plan

### Step 1: Choose Approach
**Recommendation**: Start with **Option A (TF 1.x environment)** for immediate functionality, then plan **Option B (TF 2.x migration)** for production.

### Step 2: Quick Win Implementation
1. Set up TensorFlow 1.x environment
2. Test current codebase with minimal changes
3. Validate full pipeline functionality

### Step 3: Plan Long-term Migration
1. Create detailed TF 2.x migration roadmap
2. Set up parallel development environment
3. Implement gradual migration strategy

## Risk Assessment

### Option A Risks:
- **Low Risk**: Minimal code changes, proven compatibility
- **Mitigation**: Document TF 1.x dependency clearly

### Option B Risks:
- **Medium-High Risk**: Complex refactoring, potential bugs
- **Mitigation**: Thorough testing, gradual migration, fallback plan

## Success Criteria

### Option A Success:
- [ ] Full pipeline runs without errors
- [ ] Model training completes successfully
- [ ] Hyperparameter optimization works
- [ ] Results match expected outputs

### Option B Success:
- [ ] All TF 1.x code converted to TF 2.x
- [ ] Performance matches or exceeds original
- [ ] Code maintainability improved
- [ ] Full test coverage achieved

## Timeline Estimates

| Approach | Setup | Implementation | Testing | Total |
|----------|-------|----------------|---------|-------|
| Option A | 2 hours | 4 hours | 2 hours | 1 day |
| Option B | 1 day | 5-7 days | 2-3 days | 8-11 days |
| Option C | 2-3 days | 7-14 days | 3-5 days | 12-22 days |

## Conclusion

**Immediate Recommendation**: Implement **Option A** to get the system working quickly, then plan **Option B** for long-term sustainability.

This approach provides:
1. **Quick wins** with minimal risk
2. **Functional system** for immediate use
3. **Clear path forward** for modernization
4. **Reduced project risk** through incremental approach
