#!/usr/bin/env python3
"""
Quick TensorFlow 1.x Compatibility Fix Script

This script automatically reverts the TensorFlow 2.x compatibility changes
to make the code work with TensorFlow 1.x environment.

Usage:
    python quick_tf1_fix.py

Prerequisites:
    - TensorFlow 1.15.0 environment set up
    - Backup of current files (this script modifies files in place)
"""

import os
import re
import shutil
from pathlib import Path

def backup_files(files_to_modify):
    """Create backup copies of files before modification."""
    print("Creating backup copies...")
    for file_path in files_to_modify:
        if os.path.exists(file_path):
            backup_path = f"{file_path}.tf2_backup"
            shutil.copy2(file_path, backup_path)
            print(f"  Backed up: {file_path} -> {backup_path}")

def revert_tensorflow_imports(file_path):
    """Revert TensorFlow imports from TF 2.x compat to TF 1.x native."""
    if not os.path.exists(file_path):
        print(f"  Warning: File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track if any changes were made
    original_content = content
    
    # Replace TF 2.x compat imports with TF 1.x native
    content = re.sub(
        r'import tensorflow\.compat\.v1 as tf.*?\n',
        'import tensorflow as tf\n',
        content,
        flags=re.MULTILINE
    )
    
    # Remove disable_v2_behavior calls
    content = re.sub(
        r'tf\.compat\.v1\.disable_v2_behavior\(\)\n',
        '',
        content,
        flags=re.MULTILINE
    )
    
    content = re.sub(
        r'tf\.disable_v2_behavior\(\)\n',
        '',
        content,
        flags=re.MULTILINE
    )
    
    # Remove empty lines that might be left
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ Updated TensorFlow imports: {file_path}")
        return True
    else:
        print(f"  ℹ️  No changes needed: {file_path}")
        return False

def revert_session_compatibility(file_path):
    """Revert session compatibility code to TF 1.x native."""
    if not os.path.exists(file_path):
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Replace session compatibility code
    session_compat_pattern = r'''  try:
    default_keras_session = tf\.keras\.backend\.get_session\(\)
  except AttributeError:
    # For newer TensorFlow versions, create a default session
    default_keras_session = tf\.Session\(\)'''
    
    content = re.sub(
        session_compat_pattern,
        '  default_keras_session = tf.keras.backend.get_session()',
        content,
        flags=re.MULTILINE
    )
    
    # Replace set_session compatibility code
    set_session_pattern = r'''      # Set session for TensorFlow 1\.x compatibility
      try:
        tf\.keras\.backend\.set_session\(sess\)
      except AttributeError:
        # For newer TensorFlow versions, session management is automatic
        pass'''
    
    content = re.sub(
        set_session_pattern,
        '      tf.keras.backend.set_session(sess)',
        content,
        flags=re.MULTILINE
    )
    
    # Replace reset session compatibility code
    reset_session_pattern = r'''      # Reset to default session for TensorFlow 1\.x compatibility
      try:
        tf\.keras\.backend\.set_session\(default_keras_session\)
      except AttributeError:
        # For newer TensorFlow versions, session management is automatic
        pass'''
    
    content = re.sub(
        reset_session_pattern,
        '      tf.keras.backend.set_session(default_keras_session)',
        content,
        flags=re.MULTILINE
    )
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ Reverted session compatibility: {file_path}")
        return True
    else:
        print(f"  ℹ️  No session changes needed: {file_path}")
        return False

def revert_shape_compatibility(file_path):
    """Revert shape compatibility code to TF 1.x native."""
    if not os.path.exists(file_path):
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Revert complex shape handling back to simple get_shape()
    shape_pattern = r'''    # Use tf\.shape for dynamic shape or shape property for static shape
    input_shape = tf\.shape\(all_inputs\) if hasattr\(all_inputs, 'get_shape'\) else all_inputs\.shape
    input_size_actual = input_shape\[-1\] if hasattr\(input_shape, '__getitem__'\) else all_inputs\.shape\[-1\]
    
    if input_size_actual != self\.input_size:'''
    
    content = re.sub(
        shape_pattern,
        '''    if all_inputs.get_shape().as_list()[-1] != self.input_size:''',
        content,
        flags=re.MULTILINE
    )
    
    # Fix the error message too
    content = re.sub(
        r'input_size_actual, self\.input_size\)',
        'all_inputs.get_shape().as_list()[-1], self.input_size)',
        content
    )
    
    # Revert other shape compatibility fixes
    content = re.sub(
        r'embedding_shape = tf\.shape\(embedding\) if hasattr\(embedding, \'get_shape\'\) else embedding\.shape\n      _, num_static, _ = embedding_shape\[0\], embedding_shape\[1\], embedding_shape\[2\]',
        '_, num_static, _ = embedding.get_shape().as_list()',
        content,
        flags=re.MULTILINE
    )
    
    content = re.sub(
        r'embedding_shape = tf\.shape\(embedding\) if hasattr\(embedding, \'get_shape\'\) else embedding\.shape\n      _, time_steps, embedding_dim, num_inputs = embedding_shape\[0\], embedding_shape\[1\], embedding_shape\[2\], embedding_shape\[3\]',
        '_, time_steps, embedding_dim, num_inputs = embedding.get_shape().as_list()',
        content,
        flags=re.MULTILINE
    )
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ Reverted shape compatibility: {file_path}")
        return True
    else:
        print(f"  ℹ️  No shape changes needed: {file_path}")
        return False

def main():
    """Main function to apply TF 1.x compatibility fixes."""
    print("TensorFlow 1.x Compatibility Fix")
    print("=" * 40)
    
    # Files that need to be modified
    files_to_modify = [
        'libs/utils.py',
        'libs/tft_model.py',
        'script_train_fixed_params.py',
        'script_hyperparam_opt.py'
    ]
    
    # Check if files exist
    existing_files = [f for f in files_to_modify if os.path.exists(f)]
    missing_files = [f for f in files_to_modify if not os.path.exists(f)]
    
    if missing_files:
        print("Warning: Some files not found:")
        for f in missing_files:
            print(f"  - {f}")
        print()
    
    if not existing_files:
        print("Error: No files found to modify!")
        return 1
    
    # Create backups
    backup_files(existing_files)
    print()
    
    # Apply fixes
    print("Applying TensorFlow 1.x compatibility fixes...")
    
    changes_made = False
    
    for file_path in existing_files:
        print(f"\nProcessing: {file_path}")
        
        # Apply different fixes based on file
        if revert_tensorflow_imports(file_path):
            changes_made = True
            
        if file_path in ['script_train_fixed_params.py', 'script_hyperparam_opt.py']:
            if revert_session_compatibility(file_path):
                changes_made = True
                
        if file_path == 'libs/tft_model.py':
            if revert_shape_compatibility(file_path):
                changes_made = True
    
    print("\n" + "=" * 40)
    if changes_made:
        print("✅ TensorFlow 1.x compatibility fixes applied successfully!")
        print("\nNext steps:")
        print("1. Ensure you're in a TensorFlow 1.15.0 environment")
        print("2. Test the pipeline:")
        print("   python -m script_download_data volatility .")
        print("   python -m script_train_fixed_params volatility . no")
        print("\nNote: Backup files created with .tf2_backup extension")
    else:
        print("ℹ️  No changes were needed - files may already be TF 1.x compatible")
    
    return 0

if __name__ == "__main__":
    exit(main())
