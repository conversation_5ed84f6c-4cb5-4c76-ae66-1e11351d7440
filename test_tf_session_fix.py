#!/usr/bin/env python3
"""
Test script to verify TensorFlow session compatibility fixes.
"""

import sys
import os

def test_tensorflow_session_compatibility():
    """Test if TensorFlow session management works with our fixes."""
    print("Testing TensorFlow session compatibility...")
    
    try:
        import tensorflow.compat.v1 as tf
        
        # Enable TensorFlow 1.x behavior
        tf.disable_v2_behavior()
        
        print("✅ TensorFlow compat.v1 imported successfully")
        
        # Test the session management fix
        try:
            default_keras_session = tf.keras.backend.get_session()
            print("✅ tf.keras.backend.get_session() works")
        except AttributeError as e:
            print(f"⚠️  tf.keras.backend.get_session() not available: {e}")
            print("   Using fallback session creation...")
            default_keras_session = tf.Session()
            print("✅ Fallback session created successfully")
        
        # Test set_session
        try:
            tf.keras.backend.set_session(default_keras_session)
            print("✅ tf.keras.backend.set_session() works")
        except AttributeError as e:
            print(f"⚠️  tf.keras.backend.set_session() not available: {e}")
            print("   This is expected in newer TensorFlow versions")
        
        # Test basic TensorFlow operations
        try:
            with tf.Graph().as_default():
                a = tf.constant(1.0)
                b = tf.constant(2.0)
                c = tf.add(a, b)
                print("✅ Basic TensorFlow operations work")
        except Exception as e:
            print(f"❌ TensorFlow operations failed: {e}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import TensorFlow: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_script_import():
    """Test if the fixed script can be imported."""
    print("\nTesting script import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Try to import the main function without executing it
        from script_hyperparam_opt import main
        print("✅ script_hyperparam_opt imported successfully")
        print("✅ main function is accessible")
        return True
        
    except Exception as e:
        print(f"❌ Failed to import script: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing TensorFlow session compatibility fixes")
    print("=" * 60)
    
    tests = [
        test_tensorflow_session_compatibility,
        test_script_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("The TensorFlow session compatibility fixes are working correctly.")
    else:
        print("❌ Some tests failed")
        print("The script may still have compatibility issues.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
