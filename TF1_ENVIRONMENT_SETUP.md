# TensorFlow 1.x Environment Setup Guide

## Overview

This guide provides instructions for setting up a TensorFlow 1.x environment to resolve compatibility issues with the Temporal Fusion Transformer (TFT) codebase. This is the **recommended immediate solution** (Option A) from the NEXT_STEPS_TF_COMPATIBILITY_FIX.md analysis.

## Why TensorFlow 1.x?

The current TFT codebase encounters this error with TensorFlow 2.x:
```
ValueError: A KerasTensor cannot be used as input to a TensorFlow function. 
A KerasTensor is a symbolic placeholder for a shape and dtype, used when 
constructing Keras Functional models or Keras Functions.
```

**Root Cause**: The code mixes TensorFlow 1.x style operations with TensorFlow 2.x Keras tensors, which is incompatible even with `tf.compat.v1.disable_v2_behavior()`.

**Solution**: Use native TensorFlow 1.x environment for guaranteed compatibility.

## Prerequisites

- **Conda or Miniconda** installed ([Download here](https://docs.conda.io/en/latest/miniconda.html))
- **Internet connection** for package downloads
- **Python 3.7** (recommended for TF 1.15 compatibility)

## Quick Setup (Automated)

### Option 1: Use the Setup Script

```bash
# Run the automated setup script
python setup_tf1_environment.py

# Or with custom parameters
python setup_tf1_environment.py --env-name my_tft_env --python-version 3.7
```

### Option 2: Manual Setup

```bash
# 1. Create conda environment
conda create -n tft_tf1 python=3.7 -y

# 2. Activate environment
conda activate tft_tf1

# 3. Install TensorFlow 1.x
pip install tensorflow==1.15.0
pip install keras==2.3.1

# 4. Install additional dependencies
pip install numpy==1.19.5 pandas scikit-learn matplotlib seaborn

# 5. Install project requirements (if available)
pip install -r requirements.txt
```

## Package Versions

| Package | Version | Reason |
|---------|---------|---------|
| tensorflow | 1.15.0 | Last stable TF 1.x release |
| keras | 2.3.1 | Compatible with TF 1.15 |
| numpy | 1.19.5 | Compatible with TF 1.15 |
| python | 3.7 | Recommended for TF 1.15 |

## Verification

After setup, verify the installation:

```bash
# Activate environment
conda activate tft_tf1

# Test TensorFlow
python -c "
import tensorflow as tf
print(f'TensorFlow version: {tf.__version__}')
print(f'Keras version: {tf.keras.__version__}')

# Test basic operation
hello = tf.constant('Hello, TensorFlow!')
sess = tf.Session()
print(f'Test result: {sess.run(hello)}')
sess.close()
"
```

Expected output:
```
TensorFlow version: 1.15.0
Keras version: 2.2.4-tf
Test result: b'Hello, TensorFlow!'
```

## Code Changes Required

After setting up the TF 1.x environment, make these minimal code changes:

### 1. Update Import Statements

**In all Python files** (`libs/utils.py`, `libs/tft_model.py`, `script_train_fixed_params.py`, `script_hyperparam_opt.py`):

```python
# BEFORE (TF 2.x compat):
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()

# AFTER (TF 1.x native):
import tensorflow as tf
```

### 2. Remove Session Compatibility Code

**In training scripts**:

```python
# REMOVE these try-catch blocks:
try:
    default_keras_session = tf.keras.backend.get_session()
except AttributeError:
    default_keras_session = tf.Session()

try:
    tf.keras.backend.set_session(sess)
except AttributeError:
    pass

# REPLACE with simple TF 1.x code:
default_keras_session = tf.keras.backend.get_session()
tf.keras.backend.set_session(sess)
```

## Usage

### Training with Fixed Parameters

```bash
# Activate environment
conda activate tft_tf1

# Run training
python -m script_train_fixed_params volatility . no
```

### Hyperparameter Optimization

```bash
# Activate environment
conda activate tft_tf1

# Run hyperparameter optimization
python -m script_hyperparam_opt volatility . no
```

### Using the Activation Script

If you used the automated setup, you can use the generated activation script:

```bash
# Make executable (Linux/Mac)
chmod +x activate_tf1_env.sh

# Run script
./activate_tf1_env.sh

# Or on Windows
bash activate_tf1_env.sh
```

## Troubleshooting

### Issue: "conda: command not found"
**Solution**: Install Miniconda or Anaconda first.

### Issue: "tensorflow 1.15.0 not found"
**Solution**: Use pip instead of conda for TensorFlow:
```bash
conda activate tft_tf1
pip install tensorflow==1.15.0
```

### Issue: "ImportError: No module named tensorflow"
**Solution**: Ensure you're in the correct environment:
```bash
conda activate tft_tf1
which python  # Should point to the conda environment
```

### Issue: "Session errors"
**Solution**: Make sure you've updated the import statements as described above.

## Performance Considerations

### GPU Support (Optional)

For GPU acceleration with TF 1.x:

```bash
# Install GPU version (requires CUDA 10.0)
pip install tensorflow-gpu==1.15.0

# Verify GPU detection
python -c "
import tensorflow as tf
print('GPU Available:', tf.test.is_gpu_available())
print('GPU Devices:', tf.config.list_physical_devices('GPU'))
"
```

### Memory Management

TF 1.x uses different memory management. Add these configurations if needed:

```python
# In your training scripts
config = tf.ConfigProto()
config.gpu_options.allow_growth = True  # Gradual GPU memory allocation
config.allow_soft_placement = True      # Automatic device placement
```

## Next Steps

1. **Immediate**: Use this TF 1.x setup for current development and production
2. **Long-term**: Plan migration to TensorFlow 2.x (see NEXT_STEPS_TF_COMPATIBILITY_FIX.md Option B)
3. **Alternative**: Consider modern libraries like PyTorch Forecasting or Darts for new projects

## Environment Management

### Switching Between Environments

```bash
# List all environments
conda env list

# Activate TF 1.x environment
conda activate tft_tf1

# Deactivate environment
conda deactivate

# Remove environment (if needed)
conda env remove -n tft_tf1
```

### Exporting Environment

```bash
# Export environment for sharing
conda activate tft_tf1
conda env export > tft_tf1_environment.yml

# Recreate environment from file
conda env create -f tft_tf1_environment.yml
```

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure you're using the correct Python/TensorFlow versions
4. Review the original error messages in NEXT_STEPS_TF_COMPATIBILITY_FIX.md

## Timeline

- **Setup time**: ~30 minutes
- **Code changes**: ~1 hour
- **Testing**: ~1 hour
- **Total**: ~2.5 hours

This represents a **quick win** solution that gets the system working immediately while planning for long-term TensorFlow 2.x migration.
