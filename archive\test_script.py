#!/usr/bin/env python3
"""
Test script to verify the download script works correctly.
"""

import subprocess
import sys
import os

def run_command(cmd, timeout=30):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def test_help():
    """Test the help functionality."""
    print("Testing help functionality...")
    code, stdout, stderr = run_command("python script_download_data.py --help", timeout=10)
    
    if code == 0 and "Data download configs" in stdout:
        print("✅ Help test passed")
        return True
    else:
        print(f"❌ Help test failed: code={code}")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
        return False

def test_invalid_experiment():
    """Test invalid experiment name handling."""
    print("Testing invalid experiment name...")
    code, stdout, stderr = run_command("python script_download_data.py invalid_name", timeout=10)
    
    if code != 0 and "invalid choice" in stderr:
        print("✅ Invalid experiment test passed")
        return True
    else:
        print(f"❌ Invalid experiment test failed: code={code}")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
        return False

def test_volatility_data_exists():
    """Test if volatility data exists."""
    print("Testing volatility data existence...")
    data_path = "outputs/data/volatility/formatted_omi_vol.csv"
    
    if os.path.exists(data_path):
        print(f"✅ Volatility data exists at {data_path}")
        
        # Check if file has content
        with open(data_path, 'r') as f:
            lines = f.readlines()
            if len(lines) > 1:  # Header + at least one data row
                print(f"✅ Data file has {len(lines)} lines")
                return True
            else:
                print(f"❌ Data file is empty or has only header")
                return False
    else:
        print(f"❌ Volatility data not found at {data_path}")
        return False

def main():
    """Run all tests."""
    print("Running tests for script_download_data.py")
    print("=" * 50)
    
    tests = [
        test_help,
        test_invalid_experiment,
        test_volatility_data_exists
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
