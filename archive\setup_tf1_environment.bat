@echo off
REM TensorFlow 1.x Environment Setup Script for Windows
REM This script sets up a TensorFlow 1.x environment to resolve compatibility issues

echo ========================================
echo TensorFlow 1.x Environment Setup
echo ========================================

set ENV_NAME=tft_tf1
set PYTHON_VERSION=3.7

echo Environment name: %ENV_NAME%
echo Python version: %PYTHON_VERSION%
echo ========================================

REM Check if conda is available
conda --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Conda not found. Please install Miniconda or Anaconda first.
    echo Download from: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✅ Conda found

REM Remove existing environment if it exists
echo Removing existing environment %ENV_NAME% (if exists)...
conda env remove -n %ENV_NAME% -y >nul 2>&1

REM Create new environment
echo Creating new environment %ENV_NAME% with Python %PYTHON_VERSION%...
conda create -n %ENV_NAME% python=%PYTHON_VERSION% -y
if errorlevel 1 (
    echo ERROR: Failed to create conda environment
    pause
    exit /b 1
)

echo ✅ Environment created successfully

REM Install TensorFlow 1.x and dependencies
echo Installing TensorFlow 1.x packages...
call conda activate %ENV_NAME%

echo Installing tensorflow==1.15.0...
pip install tensorflow==1.15.0
if errorlevel 1 (
    echo ERROR: Failed to install TensorFlow 1.15.0
    pause
    exit /b 1
)

echo Installing keras==2.3.1...
pip install keras==2.3.1
if errorlevel 1 (
    echo ERROR: Failed to install Keras 2.3.1
    pause
    exit /b 1
)

echo Installing additional dependencies...
pip install numpy==1.19.5 pandas scikit-learn matplotlib seaborn
if errorlevel 1 (
    echo ERROR: Failed to install additional dependencies
    pause
    exit /b 1
)

REM Install project requirements if available
if exist requirements.txt (
    echo Installing from requirements.txt...
    pip install -r requirements.txt
)

REM Verify installation
echo ========================================
echo Verifying installation...
echo ========================================

python -c "import tensorflow as tf; print(f'TensorFlow version: {tf.__version__}'); print(f'Keras version: {tf.keras.__version__}')"
if errorlevel 1 (
    echo ERROR: TensorFlow verification failed
    pause
    exit /b 1
)

python -c "hello = tf.constant('Hello, TensorFlow!'); sess = tf.Session(); print(f'Test result: {sess.run(hello)}'); sess.close()"
if errorlevel 1 (
    echo ERROR: TensorFlow session test failed
    pause
    exit /b 1
)

echo ========================================
echo 🎉 SETUP COMPLETE!
echo ========================================
echo ✅ Environment '%ENV_NAME%' created successfully
echo ✅ TensorFlow 1.x installed and verified
echo.
echo 📋 Next steps:
echo 1. Activate environment: conda activate %ENV_NAME%
echo 2. Run training: python -m script_train_fixed_params volatility . no
echo 3. Run hyperparameter optimization: python -m script_hyperparam_opt volatility . no
echo.
echo Press any key to exit...
pause >nul
