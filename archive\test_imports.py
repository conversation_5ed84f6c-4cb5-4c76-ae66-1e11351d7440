#!/usr/bin/env python3
"""
Test script to verify all imports work correctly.
"""

def test_tensorflow_imports():
    """Test TensorFlow imports."""
    print("Testing TensorFlow imports...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow imported successfully: {tf.__version__}")
    except ImportError as e:
        print(f"❌ Failed to import tensorflow: {e}")
        return False
    
    try:
        import tensorflow.compat.v1 as tf1
        print("✅ TensorFlow compat.v1 imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import tensorflow.compat.v1: {e}")
        return False
    
    return True

def test_project_imports():
    """Test project-specific imports."""
    print("\nTesting project imports...")
    
    imports_to_test = [
        ("expt_settings.configs", "ExperimentConfig"),
        ("libs.utils", "utils"),
        ("numpy", "np"),
        ("pandas", "pd"),
    ]
    
    success = True
    
    for module_name, alias in imports_to_test:
        try:
            if alias:
                exec(f"import {module_name} as {alias}")
                print(f"✅ {module_name} imported as {alias}")
            else:
                exec(f"import {module_name}")
                print(f"✅ {module_name} imported")
        except ImportError as e:
            print(f"❌ Failed to import {module_name}: {e}")
            success = False
    
    return success

def test_hyperparam_script_imports():
    """Test the specific imports from script_hyperparam_opt.py."""
    print("\nTesting hyperparam script imports...")
    
    try:
        # Test the exact imports from the script
        import expt_settings.configs
        import libs.utils as utils
        import numpy as np
        import pandas as pd
        import tensorflow.compat.v1 as tf
        
        print("✅ All hyperparam script imports successful")
        
        # Test accessing the classes
        ExperimentConfig = expt_settings.configs.ExperimentConfig
        print("✅ ExperimentConfig class accessible")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import from hyperparam script: {e}")
        return False
    except AttributeError as e:
        print(f"❌ Failed to access class: {e}")
        return False

def main():
    """Run all import tests."""
    print("Testing imports for TFT project")
    print("=" * 50)
    
    tests = [
        test_tensorflow_imports,
        test_project_imports,
        test_hyperparam_script_imports
    ]
    
    all_passed = True
    
    for test in tests:
        try:
            if not test():
                all_passed = False
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            all_passed = False
        print()
    
    print("=" * 50)
    if all_passed:
        print("🎉 All import tests passed!")
        print("The Pylance error is likely a false positive.")
        print("\nTo fix Pylance:")
        print("1. Restart VS Code")
        print("2. Check Python interpreter (Ctrl+Shift+P -> Python: Select Interpreter)")
        print("3. Use the pyrightconfig.json file created in this directory")
    else:
        print("❌ Some import tests failed")
        print("Please check your Python environment and package installations.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
