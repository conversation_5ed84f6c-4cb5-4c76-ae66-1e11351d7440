#!/usr/bin/env python3
"""
Debug script to check data loading issues.
"""

import pandas as pd
import sys
import os

def debug_volatility_data():
    """Debug the volatility data loading."""
    print("Debugging volatility data loading...")
    
    # Load the data
    data_path = "outputs/data/volatility/formatted_omi_vol.csv"
    
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        return False
    
    print(f"✅ Loading data from: {data_path}")
    df = pd.read_csv(data_path)
    
    print(f"Data shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Data types:\n{df.dtypes}")
    print(f"First few rows:\n{df.head()}")
    
    # Check for required columns
    required_columns = ['Symbol', 'date', 'log_vol', 'open_to_close', 'days_from_start', 
                       'day_of_week', 'day_of_month', 'week_of_year', 'month', 'Region']
    
    missing_columns = []
    for col in required_columns:
        if col not in df.columns:
            missing_columns.append(col)
    
    if missing_columns:
        print(f"❌ Missing columns: {missing_columns}")
        return False
    else:
        print("✅ All required columns present")
    
    # Test the data splitting logic
    print("\nTesting data splitting logic...")
    
    # Convert date column to datetime if it's not already
    if df['date'].dtype == 'object':
        df['date'] = pd.to_datetime(df['date'])
        print("✅ Converted date column to datetime")
    
    # Add year column if not present
    if 'year' not in df.columns:
        df['year'] = df['date'].dt.year
        print("✅ Added year column")
    
    print(f"Year range: {df['year'].min()} - {df['year'].max()}")
    print(f"Unique years: {sorted(df['year'].unique())}")
    
    # Test the filtering logic
    valid_boundary = 2022
    test_boundary = 2023
    
    index = df['year']
    train = df.loc[index < valid_boundary]
    valid = df.loc[(index >= valid_boundary) & (index < test_boundary)]

    # Fix the test boundary to use date column instead of index
    test_date_boundary = pd.to_datetime('2023-12-31')
    test = df.loc[(index >= test_boundary) & (df['date'] <= test_date_boundary)]
    
    print(f"\nData split results:")
    print(f"Train data shape: {train.shape}")
    print(f"Valid data shape: {valid.shape}")
    print(f"Test data shape: {test.shape}")
    
    # Check if train data has the Symbol column
    if train.empty:
        print("❌ Train data is empty!")
        return False
    elif 'Symbol' not in train.columns:
        print("❌ Train data missing Symbol column!")
        print(f"Train columns: {list(train.columns)}")
        return False
    else:
        print("✅ Train data has Symbol column")
        print(f"Train symbols: {train['Symbol'].unique()}")
    
    return True

def main():
    """Run the debug test."""
    print("Volatility Data Loading Debug")
    print("=" * 40)
    
    if debug_volatility_data():
        print("\n🎉 Data loading debug completed successfully!")
        return 0
    else:
        print("\n❌ Data loading debug found issues!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
