# TensorFlow Compatibility - Code Examples and Implementation Guide

## Quick Fix: TensorFlow 1.x Environment Setup

### Environment Setup Commands
```bash
# Option 1: Using Conda (Recommended)
conda create -n tft_tf1 python=3.7
conda activate tft_tf1
pip install tensorflow==1.15.0
pip install keras==2.3.1
pip install pandas==1.3.5 numpy==1.19.5 scikit-learn matplotlib seaborn

# Option 2: Using pip with virtual environment
python -m venv tft_tf1_env
# Windows:
tft_tf1_env\Scripts\activate
# Linux/Mac:
source tft_tf1_env/bin/activate

pip install tensorflow==1.15.0 keras==2.3.1 pandas numpy scikit-learn
```

### Code Changes for TF 1.x Environment

#### 1. Revert TensorFlow Imports
```python
# In libs/utils.py, libs/tft_model.py, script_train_fixed_params.py, script_hyperparam_opt.py

# Current (TF 2.x compat):
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()

# Change to (TF 1.x native):
import tensorflow as tf
```

#### 2. Remove Session Compatibility Code
```python
# In script_train_fixed_params.py and script_hyperparam_opt.py

# Remove these try-catch blocks:
try:
    default_keras_session = tf.keras.backend.get_session()
except AttributeError:
    default_keras_session = tf.Session()

# Replace with simple:
default_keras_session = tf.keras.backend.get_session()
```

#### 3. Restore Original Shape Methods
```python
# In libs/tft_model.py

# Current (TF 2.x compat):
input_shape = tf.shape(all_inputs) if hasattr(all_inputs, 'get_shape') else all_inputs.shape
input_size_actual = input_shape[-1] if hasattr(input_shape, '__getitem__') else all_inputs.shape[-1]

# Change back to (TF 1.x native):
if all_inputs.get_shape().as_list()[-1] != self.input_size:
```

## Advanced Fix: TensorFlow 2.x Migration Examples

### 1. Model Class Conversion

#### Current TF 1.x Style:
```python
class TemporalFusionTransformer:
    def build_model(self):
        with tf.variable_scope(self.name):
            all_inputs = tf.placeholder(tf.float32, [None, self.input_size])
            # ... model logic
            return model
```

#### Target TF 2.x Style:
```python
class TemporalFusionTransformerV2(tf.keras.Model):
    def __init__(self, config, **kwargs):
        super().__init__(**kwargs)
        self.config = config
        self._build_layers()
    
    def _build_layers(self):
        # Initialize all layers
        self.input_layer = tf.keras.layers.Input(shape=(self.config.input_size,))
        self.embedding_layers = {}
        self.attention_layers = {}
        
    def call(self, inputs, training=None):
        # Forward pass logic
        embeddings = self.get_tft_embeddings(inputs)
        outputs = self.apply_temporal_fusion(embeddings, training=training)
        return outputs
```

### 2. Variable Scope to Keras Layer Conversion

#### Current TF 1.x Style:
```python
def variable_selection_network(inputs, num_inputs, hidden_size, name):
    with tf.variable_scope(name):
        weights = tf.get_variable('weights', [num_inputs, hidden_size])
        bias = tf.get_variable('bias', [hidden_size])
        output = tf.nn.tanh(tf.matmul(inputs, weights) + bias)
        return output
```

#### Target TF 2.x Style:
```python
class VariableSelectionNetwork(tf.keras.layers.Layer):
    def __init__(self, num_inputs, hidden_size, **kwargs):
        super().__init__(**kwargs)
        self.num_inputs = num_inputs
        self.hidden_size = hidden_size
        
    def build(self, input_shape):
        self.weights = self.add_weight(
            name='weights',
            shape=(self.num_inputs, self.hidden_size),
            initializer='glorot_uniform',
            trainable=True
        )
        self.bias = self.add_weight(
            name='bias',
            shape=(self.hidden_size,),
            initializer='zeros',
            trainable=True
        )
        super().build(input_shape)
        
    def call(self, inputs):
        output = tf.nn.tanh(tf.matmul(inputs, self.weights) + self.bias)
        return output
```

### 3. Training Loop Conversion

#### Current TF 1.x Style:
```python
def train_model(model, train_data, valid_data):
    with tf.Session() as sess:
        sess.run(tf.global_variables_initializer())
        
        for epoch in range(num_epochs):
            for batch in train_data:
                _, loss = sess.run([train_op, loss_op], 
                                 feed_dict={inputs: batch})
```

#### Target TF 2.x Style:
```python
class TFTTrainer:
    def __init__(self, model, optimizer, loss_fn):
        self.model = model
        self.optimizer = optimizer
        self.loss_fn = loss_fn
        
    @tf.function
    def train_step(self, inputs, targets):
        with tf.GradientTape() as tape:
            predictions = self.model(inputs, training=True)
            loss = self.loss_fn(targets, predictions)
            
        gradients = tape.gradient(loss, self.model.trainable_variables)
        self.optimizer.apply_gradients(zip(gradients, self.model.trainable_variables))
        return loss
        
    def train(self, train_dataset, valid_dataset, epochs):
        for epoch in range(epochs):
            epoch_loss = 0
            num_batches = 0
            
            for batch_inputs, batch_targets in train_dataset:
                loss = self.train_step(batch_inputs, batch_targets)
                epoch_loss += loss
                num_batches += 1
                
            avg_loss = epoch_loss / num_batches
            print(f'Epoch {epoch}, Loss: {avg_loss}')
```

### 4. Tensor Operations Fix

#### Current Problematic Code:
```python
# This causes the KerasTensor error:
static_inputs = tf.keras.backend.stack(static_inputs, axis=1)
```

#### TF 2.x Compatible Fix:
```python
# Option 1: Use tf.stack directly
static_inputs = tf.stack(static_inputs, axis=1)

# Option 2: Use Keras Lambda layer
static_inputs = tf.keras.layers.Lambda(
    lambda x: tf.stack(x, axis=1)
)(static_inputs)

# Option 3: Create custom layer
class StackLayer(tf.keras.layers.Layer):
    def __init__(self, axis=1, **kwargs):
        super().__init__(**kwargs)
        self.axis = axis
        
    def call(self, inputs):
        return tf.stack(inputs, axis=self.axis)

static_inputs = StackLayer(axis=1)(static_inputs)
```

## Implementation Checklist

### Quick Fix (TF 1.x) Checklist:
- [ ] Create TF 1.x environment
- [ ] Install TensorFlow 1.15.0 and Keras 2.3.1
- [ ] Revert TF imports in all files
- [ ] Remove session compatibility code
- [ ] Test data download pipeline
- [ ] Test training pipeline
- [ ] Test hyperparameter optimization
- [ ] Validate results

### Advanced Fix (TF 2.x) Checklist:
- [ ] Create TF 2.x development environment
- [ ] Convert model class to tf.keras.Model
- [ ] Replace variable scopes with Keras layers
- [ ] Update training loop to eager execution
- [ ] Fix tensor operation compatibility
- [ ] Update data pipeline to tf.data
- [ ] Implement comprehensive testing
- [ ] Performance benchmarking
- [ ] Documentation update

## Testing Strategy

### Unit Tests for TF 2.x Migration:
```python
import unittest
import tensorflow as tf

class TestTFTModelV2(unittest.TestCase):
    def setUp(self):
        self.config = ModelConfig()
        self.model = TemporalFusionTransformerV2(self.config)
        
    def test_model_creation(self):
        # Test model can be instantiated
        self.assertIsInstance(self.model, tf.keras.Model)
        
    def test_forward_pass(self):
        # Test forward pass works
        batch_size = 32
        input_shape = (batch_size, self.config.input_size)
        dummy_input = tf.random.normal(input_shape)
        
        output = self.model(dummy_input)
        self.assertEqual(output.shape[0], batch_size)
        
    def test_training_step(self):
        # Test training step works
        trainer = TFTTrainer(self.model, optimizer, loss_fn)
        loss = trainer.train_step(dummy_inputs, dummy_targets)
        self.assertIsInstance(loss, tf.Tensor)
```

## Performance Considerations

### TF 1.x vs TF 2.x Performance:
- **TF 1.x**: Graph-based execution, potentially faster for complex models
- **TF 2.x**: Eager execution by default, easier debugging, @tf.function for optimization

### Optimization Tips for TF 2.x:
```python
# Use @tf.function for performance-critical code
@tf.function
def optimized_forward_pass(model, inputs):
    return model(inputs)

# Use tf.data for efficient data loading
dataset = tf.data.Dataset.from_tensor_slices(data)
dataset = dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)

# Use mixed precision for faster training
policy = tf.keras.mixed_precision.Policy('mixed_float16')
tf.keras.mixed_precision.set_global_policy(policy)
```

## Troubleshooting Common Issues

### Issue 1: "KerasTensor cannot be used as input"
**Solution**: Wrap TensorFlow operations in Keras layers or use tf.keras.layers.Lambda

### Issue 2: "Variable scope not found"
**Solution**: Convert to Keras layers with proper weight initialization

### Issue 3: "Session not available"
**Solution**: Use eager execution or @tf.function decorators

### Issue 4: "Shape incompatibility"
**Solution**: Use tf.shape() for dynamic shapes, .shape for static shapes
