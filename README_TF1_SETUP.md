# TensorFlow 1.x Setup - Quick Start Guide

## 🚀 Quick Setup

Choose your platform and run the appropriate setup script:

### Windows
```cmd
setup_tf1_environment.bat
```

### Linux/Mac
```bash
chmod +x setup_tf1_environment.sh
./setup_tf1_environment.sh
```

### Python (Cross-platform)
```bash
python setup_tf1_environment.py
```

## 📋 What This Fixes

This setup resolves the TensorFlow compatibility error:
```
ValueError: A KerasTensor cannot be used as input to a TensorFlow function.
```

**Root Cause**: Mixed TensorFlow 1.x/2.x API usage  
**Solution**: Native TensorFlow 1.x environment

## 🔧 Manual Setup (Alternative)

If the automated scripts don't work, follow these manual steps:

```bash
# 1. Create environment
conda create -n tft_tf1 python=3.7 -y
conda activate tft_tf1

# 2. Install TensorFlow 1.x
pip install tensorflow==1.15.0
pip install keras==2.3.1

# 3. Install dependencies
pip install numpy==1.19.5 pandas scikit-learn matplotlib seaborn

# 4. Verify installation
python -c "import tensorflow as tf; print(tf.__version__)"
```

## ✅ Verification

After setup, test the installation:

```bash
conda activate tft_tf1
python -c "
import tensorflow as tf
print(f'TensorFlow: {tf.__version__}')
hello = tf.constant('Hello!')
sess = tf.Session()
print(f'Test: {sess.run(hello)}')
sess.close()
"
```

Expected output:
```
TensorFlow: 1.15.0
Test: b'Hello!'
```

## 🏃‍♂️ Running the TFT Model

### Training with Fixed Parameters
```bash
conda activate tft_tf1
python -m script_train_fixed_params volatility . no
```

### Hyperparameter Optimization
```bash
conda activate tft_tf1
python -m script_hyperparam_opt volatility . no
```

### Using the Activation Script
```bash
# Linux/Mac
./activate_tf1_env.sh

# Then run your commands
python -m script_train_fixed_params volatility . no
```

## 📦 Package Versions

| Package | Version | Purpose |
|---------|---------|---------|
| tensorflow | 1.15.0 | Core ML framework |
| keras | 2.3.1 | High-level API |
| numpy | 1.19.5 | Numerical computing |
| python | 3.7 | Runtime environment |

## 🔍 Troubleshooting

### "conda: command not found"
Install Miniconda: https://docs.conda.io/en/latest/miniconda.html

### "tensorflow 1.15.0 not found"
```bash
conda activate tft_tf1
pip install --upgrade pip
pip install tensorflow==1.15.0
```

### "ImportError: No module named tensorflow"
```bash
# Ensure you're in the right environment
conda activate tft_tf1
which python  # Should show conda environment path
```

### GPU Support (Optional)
```bash
# For CUDA 10.0 compatible systems
pip install tensorflow-gpu==1.15.0
```

## 📈 Performance Tips

### Memory Management
```python
# Add to training scripts if needed
config = tf.ConfigProto()
config.gpu_options.allow_growth = True
config.allow_soft_placement = True
```

### Monitoring
```bash
# Monitor GPU usage
nvidia-smi

# Monitor CPU/Memory
htop
```

## 🔄 Environment Management

### List Environments
```bash
conda env list
```

### Switch Environments
```bash
conda activate tft_tf1    # Activate TF 1.x
conda activate base       # Back to base
conda deactivate          # Deactivate current
```

### Remove Environment
```bash
conda env remove -n tft_tf1
```

### Export/Import Environment
```bash
# Export
conda activate tft_tf1
conda env export > tft_tf1_environment.yml

# Import
conda env create -f tft_tf1_environment.yml
```

## 🎯 Next Steps

1. **Immediate**: Use this TF 1.x setup for current work
2. **Short-term**: Validate all model functionality
3. **Long-term**: Plan TensorFlow 2.x migration (see NEXT_STEPS_TF_COMPATIBILITY_FIX.md)

## 📞 Support

If you encounter issues:

1. Check this troubleshooting section
2. Verify all prerequisites are installed
3. Review the setup logs for error messages
4. Ensure you're using the correct Python/TensorFlow versions

## ⏱️ Timeline

- **Setup**: 15-30 minutes
- **Testing**: 15 minutes
- **Total**: 30-45 minutes

This provides a **quick win** solution for immediate TFT model functionality.
