#!/usr/bin/env python3
"""
TensorFlow 1.x Compatibility Test Script

This script tests the TensorFlow 1.x compatibility fixes to ensure:
1. TensorFlow 1.x imports work correctly
2. Session management works without errors
3. Basic TFT model components can be imported
4. No KerasTensor errors occur

Usage:
    python test_tf1_compatibility.py
"""

import sys
import traceback
from pathlib import Path


def test_tensorflow_import():
    """Test TensorFlow 1.x import and basic functionality."""
    print("🔍 Testing TensorFlow 1.x import...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow imported successfully: {tf.__version__}")
        
        # Verify it's TF 1.x
        if tf.__version__.startswith('1.'):
            print("✅ TensorFlow 1.x detected")
        else:
            print(f"⚠️  Warning: Expected TF 1.x, got {tf.__version__}")
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import TensorFlow: {e}")
        return False


def test_session_functionality():
    """Test TensorFlow session functionality."""
    print("\n🔍 Testing TensorFlow session functionality...")
    
    try:
        import tensorflow as tf
        
        # Test basic session operations
        hello = tf.constant('Hello, TensorFlow!')
        sess = tf.Session()
        result = sess.run(hello)
        sess.close()
        
        print(f"✅ Basic session operation successful: {result}")
        
        # Test Keras backend session functions
        default_session = tf.keras.backend.get_session()
        print("✅ tf.keras.backend.get_session() works")
        
        # Test session setting
        with tf.Session() as test_sess:
            tf.keras.backend.set_session(test_sess)
            print("✅ tf.keras.backend.set_session() works")
            
        return True
        
    except Exception as e:
        print(f"❌ Session functionality test failed: {e}")
        traceback.print_exc()
        return False


def test_script_imports():
    """Test that main scripts can be imported without errors."""
    print("\n🔍 Testing script imports...")
    
    scripts_to_test = [
        'libs.utils',
        'libs.tft_model',
        'script_train_fixed_params',
        'script_hyperparam_opt'
    ]
    
    success_count = 0
    
    for script in scripts_to_test:
        try:
            __import__(script)
            print(f"✅ {script} imported successfully")
            success_count += 1
        except ImportError as e:
            print(f"❌ Failed to import {script}: {e}")
        except Exception as e:
            print(f"⚠️  {script} imported with warnings: {e}")
            success_count += 1  # Count as success if import worked
    
    print(f"\n📊 Import Results: {success_count}/{len(scripts_to_test)} successful")
    return success_count == len(scripts_to_test)


def test_tft_model_creation():
    """Test basic TFT model creation without training."""
    print("\n🔍 Testing TFT model creation...")
    
    try:
        import tensorflow as tf
        from libs.tft_model import TemporalFusionTransformer
        import expt_settings.configs
        
        # Get a basic config for testing
        config = expt_settings.configs.ExperimentConfig('volatility', '.')
        
        # Create minimal parameters for model
        params = {
            'total_time_steps': 252,
            'num_encoder_steps': 168,
            'num_epochs': 1,  # Minimal for testing
            'early_stopping_patience': 5,
            'multiprocessing_workers': 1,
            'hidden_layer_size': 160,
            'dropout_rate': 0.1,
            'max_gradient_norm': 1.0,
            'learning_rate': 0.001,
            'minibatch_size': 64,
            'num_heads': 4,
            'stack_size': 1
        }
        
        # Try to create model instance
        print("Creating TFT model instance...")
        model = TemporalFusionTransformer(params, use_cudnn=False)
        print("✅ TFT model created successfully")
        
        # Test model properties
        print(f"✅ Model name: {model.name}")
        print(f"✅ Model time steps: {model.time_steps}")
        print(f"✅ Model input size: {model.input_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ TFT model creation failed: {e}")
        traceback.print_exc()
        return False


def test_keras_backend_operations():
    """Test Keras backend operations that were causing issues."""
    print("\n🔍 Testing Keras backend operations...")
    
    try:
        import tensorflow as tf
        import numpy as np
        
        # Test operations that were problematic
        test_data = np.random.random((2, 3, 4))
        
        # Test stack operation
        tensor_list = [tf.constant(test_data[i]) for i in range(2)]
        stacked = tf.keras.backend.stack(tensor_list, axis=1)
        print("✅ tf.keras.backend.stack() works")
        
        # Test concatenate operation
        concat_result = tf.keras.backend.concatenate(tensor_list, axis=-1)
        print("✅ tf.keras.backend.concatenate() works")
        
        # Test with session
        with tf.Session() as sess:
            stacked_result = sess.run(stacked)
            concat_result_val = sess.run(concat_result)
            print(f"✅ Session execution successful - stacked shape: {stacked_result.shape}")
            print(f"✅ Session execution successful - concat shape: {concat_result_val.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Keras backend operations test failed: {e}")
        traceback.print_exc()
        return False


def test_file_modifications():
    """Test that our file modifications are correct."""
    print("\n🔍 Testing file modifications...")
    
    files_to_check = [
        'libs/utils.py',
        'libs/tft_model.py', 
        'script_train_fixed_params.py',
        'script_hyperparam_opt.py'
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        if not Path(file_path).exists():
            issues_found.append(f"File not found: {file_path}")
            continue
            
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Check for old TF 2.x compat imports
        if 'tensorflow.compat.v1' in content:
            issues_found.append(f"{file_path}: Still contains tensorflow.compat.v1 import")
            
        # Check for disable_v2_behavior calls
        if 'disable_v2_behavior' in content:
            issues_found.append(f"{file_path}: Still contains disable_v2_behavior call")
            
        # Check for new TF 1.x import
        if 'import tensorflow as tf' not in content:
            issues_found.append(f"{file_path}: Missing 'import tensorflow as tf'")
    
    if issues_found:
        print("❌ File modification issues found:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ All file modifications look correct")
        return True


def main():
    """Run all compatibility tests."""
    print("=" * 60)
    print("TensorFlow 1.x Compatibility Test Suite")
    print("=" * 60)
    
    tests = [
        ("TensorFlow Import", test_tensorflow_import),
        ("Session Functionality", test_session_functionality),
        ("Script Imports", test_script_imports),
        ("File Modifications", test_file_modifications),
        ("Keras Backend Operations", test_keras_backend_operations),
        ("TFT Model Creation", test_tft_model_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! TensorFlow 1.x compatibility is working correctly.")
        print("\n📋 Next steps:")
        print("1. Set up TensorFlow 1.x environment using the setup scripts")
        print("2. Run: python -m script_train_fixed_params volatility . no")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please review the issues above.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure you're using TensorFlow 1.x environment")
        print("2. Check that all file modifications were applied correctly")
        print("3. Verify all dependencies are installed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
