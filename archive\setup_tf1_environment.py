#!/usr/bin/env python3
"""
TensorFlow 1.x Environment Setup Script

This script sets up a TensorFlow 1.x environment to resolve compatibility issues
with the Temporal Fusion Transformer (TFT) codebase.

Usage:
    python setup_tf1_environment.py [--env-name tft_tf1] [--python-version 3.7]

Requirements:
    - Conda or Miniconda installed
    - Internet connection for package downloads
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description="", check=True):
    """Run a shell command with error handling."""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=check, 
            capture_output=True, 
            text=True
        )
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr and result.returncode == 0:
            print("STDERR (warnings):")
            print(result.stderr)
            
        return result
        
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Command failed with return code {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_conda():
    """Check if conda is available."""
    try:
        result = subprocess.run(
            "conda --version", 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        print(f"✅ Conda found: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        print("❌ Conda not found. Please install Miniconda or Anaconda first.")
        print("Download from: https://docs.conda.io/en/latest/miniconda.html")
        return False


def create_environment(env_name, python_version):
    """Create a new conda environment."""
    print(f"\n🔧 Creating conda environment: {env_name}")
    
    # Remove existing environment if it exists
    run_command(
        f"conda env remove -n {env_name} -y",
        f"Removing existing environment {env_name} (if exists)",
        check=False
    )
    
    # Create new environment
    run_command(
        f"conda create -n {env_name} python={python_version} -y",
        f"Creating new environment {env_name} with Python {python_version}"
    )


def install_tensorflow_1x(env_name):
    """Install TensorFlow 1.x and related packages."""
    print(f"\n📦 Installing TensorFlow 1.x packages in {env_name}")
    
    # Core TensorFlow packages
    packages = [
        "tensorflow==1.15.0",
        "keras==2.3.1",
        "numpy==1.19.5",  # Compatible with TF 1.15
        "pandas",
        "scikit-learn",
        "matplotlib",
        "seaborn"
    ]
    
    for package in packages:
        run_command(
            f"conda run -n {env_name} pip install {package}",
            f"Installing {package}"
        )


def install_additional_dependencies(env_name):
    """Install additional project dependencies."""
    print(f"\n📦 Installing additional dependencies in {env_name}")
    
    # Check if requirements.txt exists
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        print("Found requirements.txt, installing additional dependencies...")
        run_command(
            f"conda run -n {env_name} pip install -r requirements.txt",
            "Installing from requirements.txt",
            check=False  # Don't fail if some packages conflict
        )
    else:
        print("No requirements.txt found, skipping additional dependencies.")


def create_activation_script(env_name):
    """Create a convenient activation script."""
    script_content = f"""#!/bin/bash
# TensorFlow 1.x Environment Activation Script
# Generated by setup_tf1_environment.py

echo "🚀 Activating TensorFlow 1.x environment: {env_name}"
echo "📍 Current directory: $(pwd)"

# Activate conda environment
conda activate {env_name}

# Verify TensorFlow installation
echo "🔍 Verifying TensorFlow installation..."
python -c "import tensorflow as tf; print(f'TensorFlow version: {{tf.__version__}}'); print(f'Keras version: {{tf.keras.__version__}}')"

echo "✅ Environment ready! You can now run:"
echo "   python -m script_train_fixed_params volatility . no"
echo "   python -m script_hyperparam_opt volatility . no"
"""
    
    script_path = Path("activate_tf1_env.sh")
    with open(script_path, "w") as f:
        f.write(script_content)
    
    # Make script executable on Unix systems
    if os.name != 'nt':  # Not Windows
        os.chmod(script_path, 0o755)
    
    print(f"✅ Created activation script: {script_path}")


def verify_installation(env_name):
    """Verify the TensorFlow 1.x installation."""
    print(f"\n🔍 Verifying installation in {env_name}")
    
    verification_script = """
import sys
print(f"Python version: {sys.version}")

try:
    import tensorflow as tf
    print(f"TensorFlow version: {tf.__version__}")
    print(f"TensorFlow location: {tf.__file__}")
    
    # Check if it's TF 1.x
    if tf.__version__.startswith('1.'):
        print("✅ TensorFlow 1.x detected")
    else:
        print("❌ Wrong TensorFlow version")
        
    # Test basic operations
    hello = tf.constant('Hello, TensorFlow!')
    sess = tf.Session()
    result = sess.run(hello)
    print(f"✅ Basic TF operation successful: {result}")
    sess.close()
    
except ImportError as e:
    print(f"❌ TensorFlow import failed: {e}")
    
try:
    import keras
    print(f"✅ Keras version: {keras.__version__}")
except ImportError as e:
    print(f"❌ Keras import failed: {e}")
    
try:
    import pandas as pd
    import numpy as np
    import sklearn
    print("✅ Additional dependencies (pandas, numpy, sklearn) imported successfully")
except ImportError as e:
    print(f"❌ Additional dependencies failed: {e}")
"""
    
    run_command(
        f"conda run -n {env_name} python -c \"{verification_script}\"",
        "Verifying TensorFlow 1.x installation"
    )


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(
        description="Set up TensorFlow 1.x environment for TFT compatibility"
    )
    parser.add_argument(
        "--env-name", 
        default="tft_tf1", 
        help="Name for the conda environment (default: tft_tf1)"
    )
    parser.add_argument(
        "--python-version", 
        default="3.7", 
        help="Python version for the environment (default: 3.7)"
    )
    
    args = parser.parse_args()
    
    print("🔧 TensorFlow 1.x Environment Setup")
    print("=" * 50)
    print(f"Environment name: {args.env_name}")
    print(f"Python version: {args.python_version}")
    print("=" * 50)
    
    # Check prerequisites
    if not check_conda():
        return 1
    
    try:
        # Setup steps
        create_environment(args.env_name, args.python_version)
        install_tensorflow_1x(args.env_name)
        install_additional_dependencies(args.env_name)
        create_activation_script(args.env_name)
        verify_installation(args.env_name)
        
        print("\n" + "=" * 60)
        print("🎉 SETUP COMPLETE!")
        print("=" * 60)
        print(f"✅ Environment '{args.env_name}' created successfully")
        print(f"✅ TensorFlow 1.x installed and verified")
        print(f"✅ Activation script created: activate_tf1_env.sh")
        print("\n📋 Next steps:")
        print(f"1. Activate environment: conda activate {args.env_name}")
        print("2. Or use the script: ./activate_tf1_env.sh")
        print("3. Run the TFT training: python -m script_train_fixed_params volatility . no")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
